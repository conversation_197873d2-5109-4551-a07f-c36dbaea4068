from model import GeoExpert, IndustryAnalysor
from config import config
from web_crawler import SafeWebCrawler
from fastapi import FastAPI, HTTPException
import uvicorn

#初始化
crawler = SafeWebCrawler(config.WebCrawlerConfig)
geo_expert = GeoExpert(config.GEOExpertConfig)
industry_analysor = IndustryAnalysor(config.IndustryAnalysorConfig)
app = FastAPI()


def website_analysis(url):
    # 爬取网页
    crawled_text = crawler.crawl(url)

    if not crawled_text:
        return "没有爬取到任何内容"

    # 合并所有页面的文本内容
    combined_text = ""
    for url_key, content in crawled_text.items():
        combined_text += f"URL: {url_key}\n内容: {content}\n\n"

    print(f"✅ 成功爬取 {len(crawled_text)} 个页面，总文本长度: {len(combined_text)} 字符")
    import sys
    sys.stdout.flush()

    # 提取关键词
    keywords = geo_expert.keyword_extract(combined_text)
    industry = industry_analysor.industry_analysis(combined_text)

    #合并输出
    summary = {}
    if isinstance(keywords, dict):
        summary.update(keywords)

    if isinstance(industry, dict):
        summary.update(industry)
    return summary

@app.get("/website_analysis")
async def analyze_website(url: str):
    try:
        return website_analysis(url)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host=config.AppConfig.host, port=config.AppConfig.port)