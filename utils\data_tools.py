import json
import os 

def load_prompt(prompt_file_path):
    """
    加载提示词
    :return: 提示词
    """
    if not os.path.exists(prompt_file_path):
        raise FileNotFoundError(f"Prompt file not found: {prompt_file_path}")
    # 获取当前文件所在目录
    with open(prompt_file_path, "r", encoding="utf-8") as f:
        prompt = f.read()
        return prompt
    
def load_json_file(json_file_path):
    """
    加载JSON文件
    :return: JSON对象
    """
    if not os.path.exists(json_file_path):
        raise FileNotFoundError(f"JSON file not found: {json_file_path}")
    with open(json_file_path, "r", encoding="utf-8") as f:
        json_obj = json.load(f)
        return json_obj
    
def write_json_file(json_file_path, json_obj):
    """
    写入JSON文件
    :return: None
    """
    with open(json_file_path, "w", encoding="utf-8") as f:
        json.dump(json_obj, f, ensure_ascii=False, indent=4)
    