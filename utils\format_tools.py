import json
import re
def extract_dict_from_string(text):
    """
    从字符串中提取JSON对象
    :param text: 包含JSON的字符串
    :return: 解析后的JSON对象，如果失败返回None
    """
    if not text:
        return None

    try:
        # 尝试直接解析整个字符串
        return json.loads(text.strip())
    except json.JSONDecodeError:
        pass

    try:
        # 获取{}中的内容，使用非贪婪匹配
        match = re.search(r'\{.*?\}', text, re.DOTALL)
        if match:
            json_str = match.group()
            return json.loads(json_str)
    except (json.JSONDecodeError, AttributeError):
        pass

    try:
        # 尝试贪婪匹配（原来的逻辑）
        match = re.search(r'\{.*\}', text, re.DOTALL)
        if match:
            json_str = match.group()
            return json.loads(json_str)
    except (json.JSONDecodeError, AttributeError):
        pass

    return None


def extract_list_from_string(text):
    """
    从字符串中提取JSON数组
    :param text: 包含JSON数组的字符串
    :return: 解析后的JSON数组，如果失败返回None
    """
    if not text:
        return None
    try:
        # 尝试直接解析整个字符串
        return json.loads(text.strip())
    except json.JSONDecodeError:
        pass
    try:
        # 获取[]中的内容，使用非贪婪匹配
        match = re.search(r'\[.*?\]', text, re.DOTALL)
        if match:
            json_str = match.group()
            return json.loads(json_str)
    except (json.JSONDecodeError, AttributeError):
        pass
    try:
        # 尝试贪婪匹配（原来的逻辑）
        match = re.search(r'\[.*\]', text, re.DOTALL)
        if match:
            json_str = match.group()
            return json.loads(json_str)
    except (json.JSONDecodeError, AttributeError):
        pass
    return None