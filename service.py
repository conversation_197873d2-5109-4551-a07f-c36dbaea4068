from model import GeoExpert, IndustryAnalysor
from config import config
from web_crawler import SafeWebCrawler

crawler = SafeWebCrawler(config.WebCrawlerConfig)
geo_expert = GeoExpert(config.GEOExpertConfig)
industry_analysor = IndustryAnalysor(config.IndustryAnalysor)

def crawl_and_summarize(url):
    """
    爬取网页并提取关键词
    :param url: 要爬取的网址
    :return: 提取的关键词
    """
    # 爬取网页
    crawled_text = crawler.crawl(url)

    if not crawled_text:
        return "没有爬取到任何内容"

    # 合并所有页面的文本内容
    combined_text = ""
    for url_key, content in crawled_text.items():
        combined_text += f"URL: {url_key}\n内容: {content}\n\n"

    print(f"✅ 成功爬取 {len(crawled_text)} 个页面，总文本长度: {len(combined_text)} 字符")

    # 提取关键词
    keywords = geo_expert.keyword_extract(combined_text)
    return keywords


if __name__ == "__main__":
    # 测试原始目标网站（确保使用正确的URL格式）
    url = "https://www.ad-link.cn"
    print(f"🕷️ 开始爬取和分析网站: {url}")

    try:
        keywords = crawl_and_summarize(url)
        print("\n提取的关键词:")
        print(keywords)
    except Exception as e:
        print(f"处理过程中出现错误: {e}")