from openai import OpenAI
from config import config
import json
from utils import extract_dict_from_string, extract_list_from_string, load_prompt, load_json_file, write_json_file

api_config = config.ModelApiConfig
client = OpenAI(api_key=api_config.api_key, base_url=api_config.base_url)

class Model:
    def __init__(self, config):
        """
        初始化
        :param config: 配置文件
        :return: None
        """
        self.api_key = api_config.api_key
        self.base_url = api_config.base_url
        self.model = api_config.model
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)
        self.config = config
    
    def check_format(self, messages):
        """
        检查消息格式
        :param messages: 消息列表
        :return: None
        """
        if not isinstance(messages, list):
            raise ValueError("messages must be a list")
        for message in messages:
            if not isinstance(message, dict):
                raise ValueError("message must be a dict")
            if "role" not in message:
                raise ValueError("message must have a role")
            if "content" not in message:
                raise ValueError("message must have a content")

    def response(self, messages):
        """
        大模型回复
        :param messages: 消息列表
        :return: 回复内容
        """
        self.check_format(messages)
        response = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            stream=False
        )
        return response.choices[0].message.content
    

class GeoExpert(Model):
    def __init__(self, config):
        """
        初始化
        :param config: 配置文件
        :return: None
        """
        super().__init__(config)

    def keyword_extract(self, text):
        """
        提取网页知识并总结营销热点
        :param text: 网页内容
        :return: 营销热点
        """
        messages = [
            {"role": "system", "content": load_prompt(self.config.prompt_file_path)},
            {"role": "user", "content": text}
        ]
        response = self.response(messages)
        return {"keywords": extract_list_from_string(response)}
    

class IndustryAnalysor(Model):
    def __init__(self, config):
        """
        初始化
        :param config: 配置文件
        :return: None
        """
        super().__init__(config)

    def _is_valid_answer(self, answer):
        """
        检测输出格式是否正确
        :param answer: 输出内容
        :return: 是否正确
        """
        try:
            industry_result = extract_dict_from_string(answer)
            if industry_result is None:
                return False
            if not isinstance(industry_result, dict):
                return False
            if "industry" not in industry_result.keys():
                return False
            if "subCategory" not in industry_result.keys():
                return False
            if industry_result["industry"] in ["其它行业", "其他行业", "其它", "其余", "其余行业"]:
                return False
            return True
        except json.JSONDecodeError:
            return False
        
    def _append_supplement_industry(self, answer):
        """
        将补充的行业分类追加到supplement_from_gpt中
        :param answer: 输出内容
        :return: None
        """
        supplement = load_json_file(self.config.supplement_file_path)
        industry_result = extract_dict_from_string(answer)
        supplement.append(industry_result)
        write_json_file(self.config.supplement_file_path, supplement)

    
    def industry_analysis(self, text):
        """
        行业分析
        :param text: 网页内容
        :return: 行业分类
        """
        #在ground_truth中查找行业分类
        ground_truth = load_json_file(self.config.ground_truth_file_path)
        prompt = load_prompt(self.config.prompt_file_path)
        backup_plan = """当你无法从行业列列表中找到匹配的行业，返回{"industry": "其他行业", "subCategory": "其他"}"""
        if "{{ground_truth}}" not in prompt:
            raise ValueError("prompt must contain {{ground_truth}}")
        if "{{backup_plan}}" not in prompt:
            raise ValueError("prompt must contain {{backup_plan}}")
        prompt_of_truth = prompt.replace("{{ground_truth}}", json.dumps(ground_truth, ensure_ascii=False))
        prompt_of_truth = prompt_of_truth.replace("{{backup_plan}}", backup_plan)
        messages = [
            {"role": "system", "content": prompt_of_truth},
            {"role": "user", "content": text}
        ]
        response = self.response(messages)
        if self._is_valid_answer(response):
            industry_result = extract_dict_from_string(response)
            industry_result["infer_type"] = "ground_truth"
            return industry_result
        
        #如果在ground_truth中没有找到合适的分类，则使用supplement中的分类
        supplement = load_json_file(self.config.supplement_file_path)
        backup_plan = """当你无法从行业列列表中找到匹配的行业，你可以填写一个你认为正确的行业和二级分类"""
        prompt_of_supplement = prompt.replace("{{ground_truth}}", json.dumps(supplement, ensure_ascii=False))
        prompt_of_supplement = prompt_of_supplement.replace("{{backup_plan}}", backup_plan)
        messages = [
            {"role": "system", "content": prompt_of_supplement},
            {"role": "user", "content": text}
        ]
        response = self.response(messages)
        if self._is_valid_answer(response):
            industry_result = extract_dict_from_string(response)
            self._append_supplement_industry(response)
            industry_result["infer_type"] = "supplement"
            return industry_result
        return {"industry": "其他行业", "subCategory": "其他", "infer_type": "default"}
        
        
        


    
