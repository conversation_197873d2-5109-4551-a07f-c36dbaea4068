from openai import OpenAI
from config import config

client = OpenAI(api_key=config.ModelApiConfig.api_key, base_url=config.ModelApiConfig.base_url)

def load_prompt():
    """
    加载提示词
    :return: 提示词
    """
    import os
    # 获取当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    prompt_path = os.path.join(current_dir, "prompt.txt")

    with open(prompt_path, "r", encoding="utf-8") as f:
        prompt = f.read()
        return prompt

class Model:
    def __init__(self, config):
        """
        初始化
        :param config: 配置文件
        :return: None
        """
        self.api_key = config.ModelApiConfig.api_key
        self.base_url = config.ModelApiConfig.base_url
        self.model = config.ModelApiConfig.model
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)
    
    def check_format(self, messages):
        """
        检查消息格式
        :param messages: 消息列表
        :return: None
        """
        if not isinstance(messages, list):
            raise ValueError("messages must be a list")
        for message in messages:
            if not isinstance(message, dict):
                raise ValueError("message must be a dict")
            if "role" not in message:
                raise ValueError("message must have a role")
            if "content" not in message:
                raise ValueError("message must have a content")

    def response(self, messages):
        """
        大模型回复
        :param messages: 消息列表
        :return: 回复内容
        """
        self.check_format(messages)
        response = self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            stream=False
        )
        return response.choices[0].message.content
    

class GeoExpert(Model):
    def __init__(self, config):
        """
        初始化
        :param config: 配置文件
        :return: None
        """
        super().__init__(config)

    def keyword_extract(self, text):
        """
        提取网页知识并总结营销热点
        :param text: 网页内容
        :return: 营销热点
        """
        messages = [
            {"role": "system", "content": load_prompt()},
            {"role": "user", "content": text}
        ]
        return self.response(messages)

    
