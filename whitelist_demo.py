"""
白名单功能演示
展示如何使用白名单跳过 robots.txt 验证
"""

from web_crawler import SafeWebCrawler
from config import config

def demo_whitelist_feature():
    print("🔐 爬虫白名单功能演示")
    print("=" * 50)
    
    # 创建爬虫实例
    crawler = SafeWebCrawler(config)
    
    # 显示白名单信息
    print(f"📋 已加载白名单域名 ({len(crawler.whitelist)} 个):")
    for domain in sorted(crawler.whitelist):
        print(f"  ✅ {domain}")
    
    print(f"\n🔍 白名单功能说明:")
    print("  • 白名单中的域名将跳过 robots.txt 验证")
    print("  • 支持主域名和子域名匹配")
    print("  • 可以在 web_crawler/whitelist.txt 中添加更多域名")
    
    # 测试不同类型的URL
    test_cases = [
        {
            "url": "https://httpbin.org/html",
            "description": "白名单网站 - 应该跳过 robots.txt"
        },
        {
            "url": "https://example.com",
            "description": "白名单网站 - 应该跳过 robots.txt"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📍 测试 {i}: {case['description']}")
        print(f"URL: {case['url']}")
        print("-" * 40)
        
        try:
            # 检查是否在白名单中
            is_whitelisted = crawler.is_domain_whitelisted(case['url'])
            status = "✅ 在白名单中" if is_whitelisted else "❌ 不在白名单中"
            print(f"白名单状态: {status}")
            
            if is_whitelisted:
                print("🕷️ 开始爬取...")
                results = crawler.crawl(case['url'])
                
                if results:
                    print(f"✅ 爬取成功: {len(results)} 个页面")
                    total_chars = sum(len(content) for content in results.values())
                    print(f"📄 总内容长度: {total_chars} 字符")
                else:
                    print("⚠️ 没有爬取到内容")
            else:
                print("⏭️ 跳过爬取（不在白名单中）")
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")
    
    print(f"\n📝 如何添加新的白名单域名:")
    print("  1. 编辑 web_crawler/whitelist.txt 文件")
    print("  2. 每行添加一个域名（如：example.com）")
    print("  3. 支持注释行（以 # 开头）")
    print("  4. 重启程序即可生效")

if __name__ == "__main__":
    demo_whitelist_feature()
