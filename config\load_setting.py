import yaml
from pydantic import BaseModel, Field

class ModelApiConfig(BaseModel):
    """
    大模型API配置
    """
    api_key: str = Field(..., description="DEEPSEEK_API_KEY")
    base_url: str = Field(..., description="DEEPSEEK_BASE_URL")
    model: str = Field(..., description="DEEPSEEK_MODEL")

class WebCrawlerConfig(BaseModel):
    """
    网络爬虫配置
    """
    max_depth: int = Field(..., description="最大爬取深度")
    max_requests: int = Field(..., description="最大爬取链接数")
    max_requests_per_second: int = Field(..., description="每秒最大请求数")

class GEOExpertConfig(BaseModel):
    """
    GEO专家配置
    """
    prompt_file_path: str = Field(..., description="提示词文件路径")

class IndustryAnalysorConfig(BaseModel):
    """
    行业分析配置
    """
    prompt_file_path: str = Field(..., description="提示词文件路径")
    ground_truth_file_path: str = Field(..., description="人工校验过的行业分类文件路径")
    supplement_file_path: str = Field(..., description="大模型补充的行业分类文件路径")


class Config(BaseModel):
    """
    配置文件
    """
    ModelApiConfig: ModelApiConfig
    WebCrawlerConfig: WebCrawlerConfig
    GEOExpertConfig: GEOExpertConfig
    IndustryAnalysorConfig: IndustryAnalysorConfig

def load_setting():
    """
    加载配置文件
    :return: 配置文件
    """
    with open("config/settings.yaml", "r") as f:
        settings = yaml.safe_load(f)
        return Config(**settings)
    
config = load_setting()